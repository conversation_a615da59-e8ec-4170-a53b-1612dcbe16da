import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import { AuthProvider } from "./contexts/AuthContext"
import { NotificationProvider } from "./contexts/NotificationContext"
import { CartProvider } from "./contexts/CartContext"
import Layout from "./components/Layout"
import LandingPage from "./pages/LandingPage"
import Login from "./pages/Login"
import Register from "./pages/Register"
import Dashboard from "./pages/Dashboard"
import Exams from "./pages/Exams"
import ExamRoom from "./pages/ExamRoom"
import StudyMaterials from "./pages/StudyMaterials"
import Profile from "./pages/Profile"
import AdminDashboard from "./pages/admin/AdminDashboard"
import AdminUsers from "./pages/admin/AdminUsers"
import AdminExams from "./pages/admin/AdminExams"
import AdminMaterials from "./pages/admin/AdminMaterials"
import AdminNotifications from "./pages/admin/AdminNotifications"
import ProtectedRoute from "./components/ProtectedRoute"
import RoleBasedRedirect from "./components/RoleBasedRedirect"
import CompleteProfile from "./pages/CompleteProfile"
import ProfileGuard from "./components/ProfileGuard"
import PaymentSuccess from "./pages/PaymentSuccess"
import DailyQuiz from "./pages/DailyQuiz"
import Cart from "./pages/Cart"
import TermsOfService from "./pages/TermsOfService"
import PrivacyPolicy from "./pages/PrivacyPolicy"
import RefundPolicy from "./pages/RefundPolicy"
import AboutUs from "./pages/AboutUs"
import HelpCenter from "./pages/HelpCenter"
import ContactUs from "./pages/ContactUs"
import LatestHappenings from "./pages/LatestHappenings"
import ExamDetails from "./pages/ExamDetails"
import Results from "./pages/Results"
import Ranking from "./pages/Ranking"
import DemoExam from "./pages/DemoExam"
import "./index.css"

function App() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <CartProvider>
          <Router>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<LandingPage />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/complete-profile" element={<CompleteProfile />} />
              <Route path="/daily-quiz" element={<DailyQuiz />} />
              <Route path="/about" element={<AboutUs />} />
              <Route path="/contact" element={<ContactUs />} />
              <Route path="/help-center" element={<HelpCenter />} />
              <Route path="/terms-of-service" element={<TermsOfService />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/refund-policy" element={<RefundPolicy />} />
              <Route path="/latest-happenings" element={<LatestHappenings />} />
              <Route path="/payment-success" element={<PaymentSuccess />} />
              <Route path="/demo-exam" element={<DemoExam />} />

              {/* ExamRoom - NO Layout */}
              <Route
                path="/app/exams-start/:examId"
                element={
                  <ProtectedRoute>
                    <ExamRoom />
                  </ProtectedRoute>
                }
              />

              {/* All other /app routes with Layout */}
              <Route
                path="/app/*"
                element={
                  <ProfileGuard>
                    <RoleBasedRedirect>
                      <Layout />
                    </RoleBasedRedirect>
                  </ProfileGuard>
                }
              >
                {/* Student Routes */}
                <Route
                  index
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="daily-quiz"
                  element={
                    <ProtectedRoute>
                      <DailyQuiz />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="exams"
                  element={
                    <ProtectedRoute>
                      <Exams />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="exams/:examId"
                  element={
                    <ProtectedRoute>
                      <ExamDetails />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="ranking/:examId"
                  element={
                    <ProtectedRoute>
                      <Ranking />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="result/:resultId"
                  element={
                    <ProtectedRoute>
                      <Results />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="materials"
                  element={
                    <ProtectedRoute>
                      <StudyMaterials />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="profile"
                  element={
                    <ProtectedRoute>
                      <Profile />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="cart"
                  element={
                    <ProtectedRoute>
                      <Cart />
                    </ProtectedRoute>
                  }
                />

                {/* Admin Routes */}
                <Route
                  path="admin"
                  element={
                    <ProtectedRoute adminOnly>
                      <AdminDashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="admin/users"
                  element={
                    <ProtectedRoute adminOnly>
                      <AdminUsers />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="admin/exams"
                  element={
                    <ProtectedRoute adminOnly>
                      <AdminExams />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="admin/materials"
                  element={
                    <ProtectedRoute adminOnly>
                      <AdminMaterials />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="admin/notifications"
                  element={
                    <ProtectedRoute adminOnly>
                      <AdminNotifications />
                    </ProtectedRoute>
                  }
                />
              </Route>
            </Routes>
          </Router>
        </CartProvider>
      </NotificationProvider>
    </AuthProvider>
  )
}

export default App
