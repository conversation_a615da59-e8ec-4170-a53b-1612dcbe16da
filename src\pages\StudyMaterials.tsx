"use client";
import { useState, useEffect, useRef } from "react";
import { BookOpen, Download, Search, Star, ShoppingCart, Eye, FileText, AlertCircle, CheckCircle, Plus, X, User, Mail, Phone } from 'lucide-react';
import { useAuth } from "../contexts/AuthContext";
import { API_ENDPOINTS, API_CONFIG, apiUtils } from "../config/api";
import { PaymentService } from "../services/paymentService";
import { PAYMENT_CONFIG } from "../config/payment";
import { useCart } from "../contexts/CartContext";
import { DownloadService } from "../services/downloadService";
import { DownloadProgress } from "../components/DownloadProgress";

const { getAuth } = await import("firebase/auth");

// Initialize services
const paymentService = new PaymentService(PAYMENT_CONFIG);
const downloadService = new DownloadService();

// Interfaces
interface Purchase {
  id: string;
  userId: string;
  materialId: string;
  amount: number;
  paymentId: string;
  orderId: string;
  status: string;
  purchaseDate: string;
  paymentMethod: string;
  gateway?: string;
  downloadCount?: number;
  lastDownloadAt?: string;
}

interface DownloadHistory {
  materialId: string;
  downloadDate: string;
  downloadUrl: string;
  fileName: string;
}

interface UserProfileMinimal {
  userId: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
}

function StudyMaterials() {
  const { user } = useAuth();
  const { addToCart, isInCart, removeFromCart } = useCart();
  const [subjectFilter, setSubjectFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [priceFilter, setPriceFilter] = useState("all");
  const [paymentLoading, setPaymentLoading] = useState<number | null>(null);
  const [downloadLoading, setDownloadLoading] = useState<number | null>(null);
  const [paymentError, setPaymentError] = useState("");
  const [materials, setMaterials] = useState<any[]>([]);
  const [userPurchases, setUserPurchases] = useState<Purchase[]>([]);
  const [downloadHistory, setDownloadHistory] = useState<DownloadHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [activeDownloads, setActiveDownloads] = useState<Map<string, string>>(new Map());
  const [downloadLimits, setDownloadLimits] = useState<Map<string, { remaining: number; used: number }>>(new Map());
  const [downloadingMaterials, setDownloadingMaterials] = useState<Set<string>>(new Set());
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [profileData, setProfileData] = useState<UserProfileMinimal>({ userId: null, name: null, email: null, phone: null });
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState("");
  const [pendingMaterial, setPendingMaterial] = useState<any>(null);
  const [successAlert, setSuccessAlert] = useState("");
  const [profileFetched, setProfileFetched] = useState(false);
  const [profileValid, setProfileValid] = useState(false);

  // Refs to prevent duplicate operations
  const downloadInProgressRef = useRef<Set<string>>(new Set());
  const purchasesFetchedRef = useRef(false);
  const profileFetchedRef = useRef(false);
  const paymentCallbackHandledRef = useRef(false);
  const reconciliationProcessedRef = useRef(false);

  useEffect(() => {
    fetchMaterials();
  }, []);

  useEffect(() => {
    if (user && !purchasesFetchedRef.current) {
      purchasesFetchedRef.current = true;
      fetchUserPurchases();
      loadDownloadHistory();
    }
  }, [user]);

  useEffect(() => {
    if (user && materials.length > 0) {
      fetchDownloadLimits();
      // Removed fetchRazorpayOrders() - This was causing the auto-initiation issue
    }
  }, [user, materials]);

  useEffect(() => {
    console.log("🔍 Profile fetch useEffect", {
      user: !!user,
      profileFetched: profileFetched,
      profileFetchedRef: profileFetchedRef.current,
    });
    if (user && !profileFetchedRef.current && !profileFetched) {
      profileFetchedRef.current = true;
      checkUserProfileOnPageLoad();
    }
  }, [user]);

  // ✨ NEW: Two-Step Reconciliation useEffect
  useEffect(() => {
    console.log("🔄 Reconciliation useEffect triggered", {
      user: !!user,
      profileValid,
      reconciliationProcessed: reconciliationProcessedRef.current
    });

    if (user && profileValid && !reconciliationProcessedRef.current) {
      reconciliationProcessedRef.current = true;
      handlePendingTransactionReconciliation();
    }
  }, [user, profileValid]);

  useEffect(() => {
    if (successAlert) {
      const timer = setTimeout(() => setSuccessAlert(""), 3000);
      return () => clearTimeout(timer);
    }
  }, [successAlert]);

  // Handle payment callback for mobile payments
  useEffect(() => {
    if (!paymentCallbackHandledRef.current && user && profileValid) {
      paymentCallbackHandledRef.current = true;
      const callbackHandled = paymentService.handlePaymentCallback(
        handlePaymentSuccess,
        handlePaymentError
      );
      
      if (callbackHandled) {
        console.log('Payment callback handled');
      }
    }
  }, [user, profileValid]);

  // ✨ NEW: Handle pending transaction reconciliation
  const handlePendingTransactionReconciliation = async () => {
    console.log("🚀 Starting reconciliation process...");

    // A. Check for pending transaction
    const pendingTransaction = paymentService.getPendingTransaction();
    if (!pendingTransaction) {
      console.log("No pending transaction found");
      return;
    }

    console.log("📦 Found pending transaction:", pendingTransaction);

    try {
      // B. Step 1: Reconcile with payment microservice
      console.log("🔄 Step 1: Reconciling with payment microservice...");
      const reconcileResponse = await apiUtils.post('/api/payments/razorpay/reconcile', {
        orderId: pendingTransaction.orderId
      });

      if (!reconcileResponse.ok) {
        console.error("❌ Reconciliation failed:", reconcileResponse.status);
        throw new Error('Payment reconciliation failed');
      }

      console.log("✅ Step 1 completed: Payment reconciled successfully");

      // C. Step 2: Sync main backend
      console.log("🔄 Step 2: Syncing with main backend...");
      const syncResponse = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases/sync?userId=${user?.id}`);

      if (!syncResponse.ok) {
        console.error("❌ Sync failed:", syncResponse.status);
        throw new Error('Backend sync failed');
      }

      console.log("✅ Step 2 completed: Main backend synced successfully");

      // D. Step 3: Refresh UI data
      console.log("🔄 Step 3: Refreshing UI data...");
      await fetchUserPurchases();
      console.log("✅ Step 3 completed: UI data refreshed");

      // E. Final cleanup
      console.log("🧹 Step 4: Clearing pending transaction...");
      paymentService.clearPendingTransaction();
      console.log("✅ Reconciliation process completed successfully!");

      // Show success message
      const materialInfo = pendingTransaction.isBulk 
        ? `${pendingTransaction.materialIds?.length || 0} materials`
        : getMaterialTitle(pendingTransaction.materialId);
      
      setSuccessAlert(`Payment reconciled successfully! You can now access ${materialInfo}.`);

      // For mobile users, scroll to top to show success message
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

    } catch (error: any) {
      console.error("❌ Reconciliation process failed:", error);
      
      // F. Handle errors gracefully - don't clear localStorage on failure
      if (error.message.includes('reconciliation')) {
        console.log("💡 Payment not completed, user can try again");
        setPaymentError("Payment was not completed. Please try purchasing again.");
      } else if (error.message.includes('sync') || error.message.includes('fetch')) {
        console.log("⚠️ Sync/fetch failed, will retry on next page load");
        setPaymentError("Payment completed but data sync failed. Please refresh the page.");
        // Note: We don't clear pendingTransaction here, so it will retry next time
        reconciliationProcessedRef.current = false;
      } else {
        console.log("🤔 Unknown error, keeping transaction for retry");
        setPaymentError("An error occurred. Please refresh the page or try again.");
        reconciliationProcessedRef.current = false;
      }
    }
  };

  // Helper function to get material title from ID
  const getMaterialTitle = (materialId?: string): string => {
    if (!materialId) return "your purchase";
    const material = materials.find(m => m.id.toString() === materialId.toString());
    return material ? `"${material.title}"` : "your purchase";
  };

  const fetchUserProfile = async (): Promise<UserProfileMinimal> => {
    if (!user) {
      throw new Error("User not authenticated");
    }
    if (localStorage.getItem(`profile_fetching_${user.id}`)) {
      console.log("⏳ Profile fetch already in progress");
      return new Promise((resolve) => {
        const check = () => {
          const profile = localStorage.getItem(`profile_${user.id}`);
          if (profile) {
            resolve(JSON.parse(profile));
          } else {
            setTimeout(check, 100);
          }
        };
        check();
      });
    }
    localStorage.setItem(`profile_fetching_${user.id}`, "true");
    try {
      console.log("📡 Fetching user profile from API...");
      const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/userProfiles/me/minimal`);
      console.log("📡 Response received:", response);
      let profileData = null;
      if (response.headers.get("content-type")?.includes("application/json")) {
        profileData = await response.json();
      } else {
        const text = await response.text();
        console.error("Unexpected response format:", text);
      }
      if (!response.ok) {
        if (response.status === 404) {
          console.log("❌ No profile found, returning empty profile");
          const emptyProfile = { userId: user.id || null, name: null, email: null, phone: null };
          localStorage.setItem(`profile_${user.id}`, JSON.stringify(emptyProfile));
          return emptyProfile;
        } else if (response.status === 401) {
          throw new Error("Authentication required. Please login again.");
        } else {
          throw new Error(`Failed to fetch profile data: ${response.statusText}`);
        }
      }
      const profile = {
        userId: user.id || null,
        name: profileData.name || null,
        email: profileData.email || null,
        phone: profileData.phone || null,
      };
      localStorage.setItem(`profile_${user.id}`, JSON.stringify(profile));
      console.log("✅ Profile fetched and cached:", profile);
      return profile;
    } catch (error: any) {
      console.error("❌ Error fetching user profile:", error);
      throw new Error("Network error fetching profile. Please try again.");
    } finally {
      localStorage.removeItem(`profile_fetching_${user.id}`);
    }
  };

  const validateProfile = (profile: UserProfileMinimal): boolean => {
    const isValid = !!(profile.name && profile.name.trim() && 
                      profile.email && profile.email.trim() && 
                      profile.phone && profile.phone.trim());
    console.log("🔍 Profile validation result:", { 
      profile, 
      isValid,
      hasName: !!profile.name?.trim(),
      hasEmail: !!profile.email?.trim(),
      hasPhone: !!profile.phone?.trim()
    });
    return isValid;
  };

  const checkUserProfileOnPageLoad = async () => {
    if (!user || profileFetched) return;
    try {
      console.log("🚀 Starting immediate profile check on page load...");
      const profile = await fetchUserProfile();
      const isValid = validateProfile(profile);
      setProfileData(profile);
      setProfileFetched(true);
      setProfileValid(isValid);
      if (!isValid) {
        console.log("⚠️ Profile is incomplete - showing modal immediately");
        setShowProfileModal(true);
      } else {
        console.log("✅ Profile is complete and valid");
      }
    } catch (error: any) {
      console.error("❌ Error checking profile on page load:", error);
      setProfileFetched(true);
      setProfileValid(false);
      setShowProfileModal(true);
    }
  };

  const updateUserProfile = async (updatedProfile: UserProfileMinimal): Promise<void> => {
    if (!user) {
      throw new Error("User not authenticated");
    }
    try {
      console.log("📝 Updating user profile:", updatedProfile);
      const response = await apiUtils.patch(`${API_CONFIG.API_BASE_URL}/userProfiles/me`, {
        name: updatedProfile.name,
        email: updatedProfile.email,
        phone: updatedProfile.phone,
      });
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Authentication required. Please login again.");
        } else {
          const errorText = await response.text();
          console.error("Profile update failed:", errorText);
          throw new Error("Failed to update profile. Please try again.");
        }
      }
      console.log("✅ Profile updated successfully");
      setProfileData(updatedProfile);
      setProfileValid(validateProfile(updatedProfile));
      localStorage.setItem(`profile_${user.id}`, JSON.stringify(updatedProfile));
    } catch (error: any) {
      console.error("❌ Error updating user profile:", error);
      if (error.message.includes("fetch")) {
        throw new Error("Network error updating profile. Please try again.");
      }
      throw error;
    }
  };

  const handleProfileFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setProfileLoading(true);
    setProfileError("");
    try {
      await updateUserProfile(profileData);
      setShowProfileModal(false);
      setSuccessAlert("Profile updated successfully!");
      if (pendingMaterial) {
        setTimeout(() => {
          initiatePaymentWithProfile(pendingMaterial, profileData);
        }, 500);
      }
    } catch (error: any) {
      setProfileError(error.message || "Failed to update profile. Please try again.");
    } finally {
      setProfileLoading(false);
    }
  };

  const generateRandomOrderId = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = 'order_';
    for (let i = 0; i < 14; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const generateRandomPaymentId = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = 'pay_';
    for (let i = 0; i < 14; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleFreePurchase = async (material: any) => {
    setPaymentLoading(material.id);
    setPaymentError("");
    try {
      const orderId = generateRandomOrderId();
      const paymentId = generateRandomPaymentId();
      const auth = getAuth();
      const user = auth.currentUser;
      const token = (await user?.getIdToken()) || "";
      const purchaseResponse = await fetch(`${API_CONFIG.API_BASE_URL}/purchases`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          materialId: material.id.toString(),
          amount: 0,
          paymentMethod: "free",
          orderId: orderId,
          paymentId: paymentId,
          status: "completed",
          purchaseDate: new Date().toISOString(),
        }),
      });
      if (!purchaseResponse.ok) {
        throw new Error("Failed to save purchase");
      }
      const purchase: Purchase = {
        id: `purchase_${Date.now()}`,
        userId: user?.id || "",
        materialId: material.id.toString(),
        amount: 0,
        paymentId: paymentId,
        orderId: orderId,
        status: "completed",
        purchaseDate: new Date().toISOString(),
        paymentMethod: "free",
        gateway: undefined,
        downloadCount: 0,
      };
      const existingPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]");
      existingPurchases.push(purchase);
      localStorage.setItem("userPurchases", JSON.stringify(existingPurchases));
      setUserPurchases((prev) => [...prev, purchase]);
      setSuccessAlert("Material added to your library successfully!");
      setPaymentLoading(null);
      setPendingMaterial(null);
    } catch (error) {
      console.error("Free purchase failed:", error);
      setPaymentError("Failed to add material to your library. Please try again.");
      setPaymentLoading(null);
      setPendingMaterial(null);
    }
  };

  const initiatePaymentWithProfile = async (material: any, profile: UserProfileMinimal) => {
    if (material.price === 0) {
      await handleFreePurchase(material);
      return;
    }
    setPaymentLoading(material.id);
    setPaymentError("");
    try {
      console.log("💳 Initiating payment with profile:", profile);
      if (typeof (window as any).Razorpay !== "undefined") {
        console.log("Using Razorpay payment gateway");
        // Removed the razorpayOrders dependency - orders are now created on-demand
        await paymentService.initiateRazorpayPayment(
          material,
          profile,
          undefined, // No pre-created order
          handlePaymentSuccess,
          handlePaymentError
        );
      } else {
        console.log("Razorpay not available, using Cashfree as fallback");
        await paymentService.initiateCashfreePayment(material, profile, handlePaymentSuccess, handlePaymentError);
      }
    } catch (error) {
      handlePaymentError(error);
    }
  };

  const fetchUserPurchases = async () => {
    if (!user) return;
    try {
      const localPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]");
      const userSpecificPurchases = localPurchases.filter((p: Purchase) => p.userId.toString() === user.id.toString());
      console.log("Loaded purchases from localStorage:", {
        allPurchases: localPurchases,
        userSpecific: userSpecificPurchases,
        userId: user.id,
      });
      setUserPurchases(userSpecificPurchases);
      try {
        console.log("🔄 Step 1: Reconciling with payment microservice...");
      const reconcileResponse = await apiUtils.post(`${API_CONFIG.PAYMENT_API_BASE_URL}/api/payments/razorpay/reconcile`, {
        userId: user.id
      });
      if (reconcileResponse.ok) {
        console.log("✅ Step 1 completed: Payment reconciled successfully");
      }
        const sync = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases/sync?userId=${user.id}`)
        if (sync.ok) {
          console.log("Sync request successful");
        }
       // const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases?userId=${user.id}`);
        const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases/user/${user.id}`);
        
        if (response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const text = await response.text();
            console.log("API response:", text);
            if (text.trim()) {
              const apiPurchases = JSON.parse(text);
              console.log("API purchases:", apiPurchases);
              const userPurchasesFromAPI = Array.isArray(apiPurchases)
                ? apiPurchases.filter((p: any) => p.userId === p.userId)
                : apiPurchases.userId === user.id
                  ? [apiPurchases]
                  : [];
              if (userPurchasesFromAPI.length > 0) {
                const mergedPurchases = [...userSpecificPurchases];
                userPurchasesFromAPI.forEach((apiPurchase: any) => {
                  if (!mergedPurchases.find((p) => p.id === apiPurchase.id)) {
                    mergedPurchases.push(apiPurchase);
                  }
                });
                setUserPurchases(mergedPurchases);
                console.log("Merged purchases from API and localStorage:", mergedPurchases);
              }
            }
          }
        }
      } catch (apiError) {
        console.log("API fetch failed, using localStorage only:", apiError);
      }
    } catch (error) {
      console.error("Error loading purchases:", error);
    }
  };

  const loadDownloadHistory = () => {
    if (!user) return;
    const history = JSON.parse(localStorage.getItem(`downloadHistory_${user.id}`) || "[]");
    setDownloadHistory(history);
  };

  const saveDownloadHistory = (materialId: string, fileName: string, downloadUrl: string) => {
    if (!user) return;
    const newDownload: DownloadHistory = {
      materialId,
      downloadDate: new Date().toISOString(),
      downloadUrl,
      fileName,
    };
    const updatedHistory = [newDownload, ...downloadHistory].slice(0, 50);
    setDownloadHistory(updatedHistory);
    localStorage.setItem(`downloadHistory_${user.id}`, JSON.stringify(updatedHistory));
  };

  const isPurchased = (materialId: number | string): boolean => {
    const purchased = userPurchases.some((purchase) => {
      const match = purchase.materialId.toString() === materialId.toString() && purchase.status === "completed";
      console.log("Purchase comparison:", {
        purchaseMaterialId: purchase.materialId,
        purchaseMaterialIdType: typeof purchase.materialId,
        materialId: materialId,
        materialIdType: typeof materialId,
        statusMatch: purchase.status === "completed",
        idMatch: purchase.materialId.toString() === materialId.toString(),
        overallMatch: match,
      });
      return match;
    });
    console.log("isPurchased final result:", {
      materialId,
      userPurchases: userPurchases.map((p) => ({
        id: p.materialId,
        type: typeof p.materialId,
        status: p.status,
      })),
      purchased,
    });
    return purchased;
  };

  const canDownload = (material: any): boolean => {
    const purchased = isPurchased(material.id);
    console.log("canDownload check:", {
      materialId: material.id,
      purchased,
      hasSecureUrl: !!material.secure_url,
      hasFilePath: !!material.filePath,
    });
    if (!purchased) {
      return false;
    }
    return true;
  };

  const getPurchaseDetails = (materialId: string): Purchase | null => {
    return (
      userPurchases.find(
        (purchase) => purchase.materialId === materialId.toString() && purchase.status === "completed"
      ) || null
    );
  };

  const handleDownload = async (material: any) => {
    if (!user) {
      alert("Please login to download materials");
      return;
    }
    const materialId = material.id.toString();
    if (downloadInProgressRef.current.has(materialId)) {
      console.log("Download already in progress for material:", materialId);
      return;
    }
    if (downloadingMaterials.has(materialId)) {
      console.log("Download already in progress (state check) for material:", materialId);
      return;
    }
    const purchased = isPurchased(material.id);
    if (!purchased) {
      alert("Please purchase this material first");
      return;
    }
    try {
      downloadInProgressRef.current.add(materialId);
      setDownloadingMaterials((prev) => new Set(prev).add(materialId));
      const remaining = await downloadService.getRemainingDownloads(materialId, user.id);
      console.log("Remaining downloads for material", materialId, ":", remaining);
      if (remaining <= 0) {
        alert("Download limit exceeded! You can only download this material 5 times.");
        downloadInProgressRef.current.delete(materialId);
        setDownloadingMaterials((prev) => {
          const newSet = new Set(prev);
          newSet.delete(materialId);
          return newSet;
        });
        return;
      }
      const secureLink = await downloadService.generateSecureDownloadLink(materialId, user.id, {
        userAgent: navigator.userAgent,
      });
      console.log("Generated secure link:", secureLink);
      localStorage.setItem(`download_token_${secureLink.downloadId}`, secureLink.token);
      setActiveDownloads((prev) => new Map(prev.set(materialId, secureLink.downloadId)));
    } catch (error: any) {
      console.error("Download error:", error);
      alert(error.message || "Download failed. Please try again.");
      downloadInProgressRef.current.delete(materialId);
      setDownloadingMaterials((prev) => {
        const newSet = new Set(prev);
        newSet.delete(materialId);
        return newSet;
      });
    }
  };

  const handleDownloadComplete = async (materialId: string) => {
    console.log("Download completed for material:", materialId);
    const downloadId = activeDownloads.get(materialId);
    if (downloadId) {
      localStorage.removeItem(`download_token_${downloadId}`);
    }
    setActiveDownloads((prev) => {
      const newMap = new Map(prev);
      newMap.delete(materialId);
      return newMap;
    });
    downloadInProgressRef.current.delete(materialId);
    setDownloadingMaterials((prev) => {
      const newSet = new Set(prev);
      newSet.delete(materialId);
      return newSet;
    });
    await fetchDownloadLimits();
  };

  const handleDownloadError = (materialId: string, error: string) => {
    console.log("Download error for material:", materialId, error);
    setActiveDownloads((prev) => {
      const newMap = new Map(prev);
      newMap.delete(materialId);
      return newMap;
    });
    downloadInProgressRef.current.delete(materialId);
    setDownloadingMaterials((prev) => {
      const newSet = new Set(prev);
      newSet.delete(materialId);
      return newSet;
    });
  };

  const getDownloadCount = (materialId: string): number => {
    const purchase = getPurchaseDetails(materialId);
    return purchase?.downloadCount || 0;
  };

  const getLastDownloadDate = (materialId: string): string | null => {
    const purchase = getPurchaseDetails(materialId);
    return purchase?.lastDownloadAt || null;
  };

  const fetchDownloadLimits = async () => {
    if (!user || materials.length === 0) return;
    const limits = new Map();
    for (const material of materials) {
      if (isPurchased(material.id)) {
        try {
          const remaining = await downloadService.getRemainingDownloads(material.id.toString(), user.id);
          const used = Math.max(0, 5 - remaining);
          limits.set(material.id.toString(), {
            remaining,
            used,
          });
        } catch (error) {
          limits.set(material.id.toString(), {
            remaining: 5,
            used: 0,
          });
        }
      }
    }
    setDownloadLimits(limits);
  };

  const renderActionButton = (material: any) => {
    const purchased = isPurchased(material.id);
    const inCart = isInCart(material.id);
    const downloadCount = getDownloadCount(material.id);
    const lastDownload = getLastDownloadDate(material.id);
    const isFree = material.price === 0;
    console.log("renderActionButton:", {
      materialId: material.id,
      materialTitle: material.title,
      purchased,
      inCart,
      userPurchasesCount: userPurchases.length,
    });
    if (purchased) {
      return (
        <div className="space-y-2">
          {renderDownloadSection(material)}
          <div className="text-xs text-gray-500 text-center">
            <div className="flex items-center justify-center space-x-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Purchased</span>
            </div>
            {downloadCount > 0 && (
              <div className="mt-1">
                Downloaded {downloadCount} time{downloadCount !== 1 ? "s" : ""}
                {lastDownload && <div className="text-xs">Last: {new Date(lastDownload).toLocaleDateString()}</div>}
              </div>
            )}
          </div>
        </div>
      );
    }
    return (
      <div className="space-y-2">
        <button
          onClick={() => (inCart ? removeFromCart(material.id) : addToCart(material))}
          className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
            inCart
              ? "bg-orange-100 text-orange-700 border border-orange-300 hover:bg-orange-200"
              : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"
          }`}
        >
          {inCart ? (
            <>
              <CheckCircle className="h-4 w-4" />
              <span>In Cart</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span>Add to Cart</span>
            </>
          )}
        </button>
        <button
          onClick={() => handlePurchase(material)}
          disabled={paymentLoading === material.id}
          className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed ${
            isFree
              ? "bg-green-600 text-white hover:bg-green-700"
              : "bg-blue-600 text-white hover:bg-blue-700"
          }`}
        >
          {paymentLoading === material.id ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>{isFree ? "Adding to your library..." : "Processing..."}</span>
            </>
          ) : (
            <>
              <ShoppingCart className="h-4 w-4" />
              <span>{isFree ? "Get Free" : `Buy Now - ₹${material.price}`}</span>
            </>
          )}
        </button>
      </div>
    );
  };

  const renderDownloadSection = (material: any) => {
    const purchased = isPurchased(material.id);
    const materialId = material.id.toString();
    const activeDownloadId = activeDownloads.get(materialId);
    const limits = downloadLimits.get(materialId);
    const isDownloading = downloadingMaterials.has(materialId) || downloadInProgressRef.current.has(materialId);
    console.log(`Rendering download section for material ${materialId}:`, {
      purchased,
      limits,
      activeDownloadId,
      isDownloading,
    });
    if (!purchased) {
      return null;
    }
    if (activeDownloadId) {
      return (
        <DownloadProgress
          downloadId={activeDownloadId}
          fileName={`${material.title}.pdf`}
          onComplete={() => handleDownloadComplete(materialId)}
          onError={(error) => handleDownloadError(materialId, error)}
        />
      );
    }
    const remainingDownloads = limits?.remaining ?? 5;
    const usedDownloads = limits?.used ?? 0;
    return (
      <div className="space-y-3">
        <button
          onClick={() => handleDownload(material)}
          disabled={remainingDownloads <= 0 || isDownloading}
          className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
            remainingDownloads <= 0 || isDownloading
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-green-600 text-white hover:bg-green-700"
          }`}
        >
          {isDownloading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Preparing...</span>
            </>
          ) : (
            <>
              <Download className="h-4 w-4" />
              <span>{remainingDownloads <= 0 ? "Download Limit Reached" : "Download PDF"}</span>
            </>
          )}
        </button>
        <div className="text-xs text-center">
          <div
            className={`px-2 py-1 rounded ${
              remainingDownloads <= 1
                ? "bg-red-50 text-red-600"
                : remainingDownloads <= 2
                  ? "bg-yellow-50 text-yellow-600"
                  : "bg-green-50 text-green-600"
            }`}
          >
            {remainingDownloads} of 5 downloads remaining
          </div>
          {usedDownloads > 0 && (
            <div className="text-gray-500 mt-1">
              Downloaded {usedDownloads} time{usedDownloads !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </div>
    );
  };

  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const response = await apiUtils.get(API_ENDPOINTS.STUDY_MATERIALS);
      if (response.ok) {
        const data = await response.json();
        setMaterials(data);
      } else {
        setError("Failed to fetch study materials");
      }
    } catch (err) {
      console.error("Error fetching materials:", err);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (material: any) => {
    if (!user) {
      alert("Please login to purchase materials");
      return;
    }
    if (!profileValid) {
      console.log("⚠️ Profile not valid, showing modal");
      setPendingMaterial(material);
      setShowProfileModal(true);
      return;
    }
    console.log("🛒 Profile is valid, proceeding with payment for:", material.title);
    await initiatePaymentWithProfile(material, profileData);
  };

  const handlePaymentSuccess = async (response: any) => {
    console.log("Payment success response:", response);
    const purchase: Purchase = {
      id: `purchase_${Date.now()}`,
      userId: response.userId.toString(),
      materialId: response.materialId.toString(),
      amount: response.amount,
      paymentId: response.paymentId,
      orderId: response.orderId,
      status: "completed",
      purchaseDate: new Date().toISOString(),
      paymentMethod: response.paymentMethod || "razorpay",
      gateway: response.gateway || "razorpay",
      downloadCount: 0,
    };
    console.log("Creating purchase record:", purchase);
    const existingPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]");
    existingPurchases.push(purchase);
    localStorage.setItem("userPurchases", JSON.stringify(existingPurchases));
    console.log("Updated localStorage purchases:", existingPurchases);
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      const token = (await user?.getIdToken()) || "";
      const purchaseResponse = await fetch(`${API_CONFIG.API_BASE_URL}/purchases`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(purchase),
      });
      if (!purchaseResponse.ok) {
        console.error("Failed to save purchase to backend:", await purchaseResponse.text());
      } else {
        console.log("Purchase saved to backend successfully");
      }
    } catch (error) {
      console.error("Error saving purchase to backend:", error);
    }
    setUserPurchases((prev) => {
      const updated = [...prev, purchase];
      console.log("Updated userPurchases state:", updated);
      return updated;
    });
    const purchasedMaterial = materials.find((m) => m.id.toString() === response.materialId.toString());
    const materialTitle = purchasedMaterial?.title || "Study Material";
    const gatewayName = response.gateway === "cashfree" ? "Cashfree" : "Razorpay";
    setSuccessAlert(`Payment successful via ${gatewayName}! You can now download "${materialTitle}"`);
    setPaymentLoading(null);
    setPendingMaterial(null);
    setMaterials((prev) => [...prev]);
    
    // For mobile payments, scroll to top to show success message
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handlePaymentError = (error: any) => {
    console.error("Payment failed:", error);
    setPaymentError(error.message || "Payment failed. Please try again.");
    setPaymentLoading(null);
    setPendingMaterial(null);
    
    // For mobile payments, scroll to top to show error message
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const subjects = ["all", "Current Affairs", "Polity", "Economics", "Geography", "History", "Science & Technology"];
  const types = ["all", "PDF", "Video", "Audio"];
  const priceRanges = ["all", "free", "under-200", "200-500", "above-500"];

  const filteredMaterials = materials.filter((material) => {
    const matchesSearch =
      material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = subjectFilter === "all" || material.subject === subjectFilter;
    const matchesType = typeFilter === "all" || material.type === typeFilter;
    let matchesPrice = true;
    if (priceFilter === "free") matchesPrice = material.price === 0;
    else if (priceFilter === "under-200") matchesPrice = material.price < 200;
    else if (priceFilter === "200-500") matchesPrice = material.price >= 200 && material.price <= 500;
    else if (priceFilter === "above-500") matchesPrice = material.price > 500;
    return matchesSearch && matchesSubject && matchesType && matchesPrice;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading study materials...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button onClick={fetchMaterials} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Study Materials</h1>
          <p className="text-gray-600 mt-1">Premium PDF documents for UPSC preparation</p>
        </div>
        <div className="flex items-center space-x-4 mt-4 sm:mt-0">
          <div className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5 text-gray-600" />
            <span className="text-sm text-gray-600">{userPurchases.length} purchased materials</span>
          </div>
          {user && profileFetched && (
            <div className="flex items-center space-x-2">
              {profileValid ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-xs text-green-600">Profile Complete</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                  <span className="text-xs text-yellow-600">Profile Incomplete</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={subjectFilter}
            onChange={(e) => setSubjectFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {subjects.map((subject) => (
              <option key={subject} value={subject}>
                {subject === "all" ? "All Subjects" : subject}
              </option>
            ))}
          </select>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {types.map((type) => (
              <option key={type} value={type}>
                {type === "all" ? "All Types" : type}
              </option>
            ))}
          </select>
          <select
            value={priceFilter}
            onChange={(e) => setPriceFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {priceRanges.map((range) => (
              <option key={range} value={range}>
                {range === "all"
                  ? "All Prices"
                  : range === "free"
                    ? "Free"
                    : range === "under-200"
                      ? "Under ₹200"
                      : range === "200-500"
                        ? "₹200 - ₹500"
                        : "Above ₹500"}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMaterials.map((material) => (
          <div
            key={material.id}
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">{material.type}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{material.rating}</span>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{material.title}</h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{material.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>By {material.author}</span>
                <span className="bg-gray-100 px-2 py-1 rounded text-xs">{material.subject}</span>
              </div>
              <div className="flex items-center text-sm text-gray-500 mb-4">
                <span className="flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  {material.pages} pages
                </span>
              </div>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold text-gray-900">₹{material.price}</span>
                <div className="flex items-center space-x-1">
                  <Download className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-500">{material.downloads}</span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                  <Eye className="h-4 w-4" />
                </button>
                <div className="flex-1">{renderActionButton(material)}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredMaterials.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No materials found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms.</p>
        </div>
      )}

      {showProfileModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 p-4 bg-black bg-opacity-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Complete Your Profile</h3>
                <button
                  onClick={() => {
                    setShowProfileModal(false);
                    setPaymentLoading(null);
                    setPendingMaterial(null);
                    setProfileError("");
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <p className="text-gray-600 mb-6">
                Please complete your profile information to proceed with purchases.
              </p>
              <form onSubmit={handleProfileFormSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="h-4 w-4 inline mr-1" />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={profileData.name || ""}
                    onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="h-4 w-4 inline mr-1" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    required
                    value={profileData.email || ""}
                    onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your email address"
                  />
                  {profileData.email && !validateEmail(profileData.email) && (
                    <p className="text-red-500 text-xs mt-1">Please enter a valid email address</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="h-4 w-4 inline mr-1" />
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={profileData.phone || ""}
                    onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your phone number"
                    maxLength={10}
                  />
                  {profileData.phone && !validatePhone(profileData.phone) && (
                    <p className="text-red-500 text-xs mt-1">Please enter a valid 10-digit phone number</p>
                  )}
                </div>
                {profileError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-red-700 text-sm">{profileError}</span>
                    </div>
                  </div>
                )}
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowProfileModal(false);
                      setPaymentLoading(null);
                      setPendingMaterial(null);
                      setProfileError("");
                    }}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={profileLoading || !profileData.name?.trim() || !profileData.email?.trim() || !profileData.phone?.trim() || !validateEmail(profileData.email || '') || !validatePhone(profileData.phone || '')}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  >
                    {profileLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Updating...</span>
                      </>
                    ) : (
                      <span>Update Profile</span>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {successAlert && (
        <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm">{successAlert}</span>
          </div>
        </div>
      )}

      {paymentError && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm">{paymentError}</span>
          </div>
          <button 
            onClick={() => setPaymentError("")}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
}

export default StudyMaterials;