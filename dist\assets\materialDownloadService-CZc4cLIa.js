import{a as e,A as o}from"./index-B4A63TNP.js";import"./vendor-CAdiN7ib.js";import"./router-BfC3tt3M.js";import"./firebase-BBaGGrQ4.js";import"./ui-ba4HuGLl.js";class r{async downloadMaterial(r,t){try{const a=await e.getAuthHeaders(),n=await e.get(`${o.STUDY_MATERIALS}/${r}`);if(!n.ok)throw new Error(`Material not found: ${n.statusText}`);const i=await n.json(),d=await fetch(`${o.STUDY_MATERIALS}/${r}/download?userId=${t}`,{headers:a});if(d.ok){const e=await d.blob(),o=window.URL.createObjectURL(e),r=document.createElement("a");return r.href=o,r.download=i.originalName||`${i.title}.pdf`,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o),{success:!0,downloadUrl:o,fileName:i.originalName||`${i.title}.pdf`}}{const e=await d.text();throw new Error(`Download failed: ${e}`)}}catch(a){return console.error("Download failed:",a),{success:!1,error:a.message||"Unknown error occurred"}}}}const t=new r;export{r as MaterialDownloadService,t as materialDownloadService};
